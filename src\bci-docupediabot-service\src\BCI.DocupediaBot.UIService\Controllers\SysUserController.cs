﻿using BCI.DocupediaBot.Application.Contracts.Dtos.SysUser;
using BCI.DocupediaBot.Application.Services.SysUser;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Infrastructure.Constants;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.UIService.Controllers
{
  [Authorize]
  [ApiController]
  [Route("api/[controller]")]
  public class SysUserController : ControllerBase
  {
    private readonly ISysUserService _sysUserService;
    private readonly ILogger<SysUserController> _logger;

    public SysUserController(ISysUserService sysUserService, ILogger<SysUserController> logger)
    {
      _sysUserService = sysUserService ?? throw new ArgumentNullException(nameof(sysUserService));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }





    [HttpGet]
    public async Task<IActionResult> GetAllAsync()
    {
      try
      {
        _logger.LogInformation("Querying all users");
        var users = await _sysUserService.QueryUsersAsync();
        return Ok(ApiResponse<List<SysUserResponseDTO>>.Ok(users));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query users");
        return StatusCode(500, ApiResponse<string>.Error(ErrorMessages.InternalServerError));
      }
    }






    [HttpGet("{id}")]
    public async Task<IActionResult> GetByIdAsync(Guid id)
    {
      if (id == Guid.Empty)
      {
        _logger.LogWarning("Invalid user query request: {Message}", ErrorMessages.UserIdEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.UserIdEmpty));
      }

      try
      {
        _logger.LogInformation("Querying user with groups for ID: {UserId}", id);
        var user = await _sysUserService.QueryUserWithGroupsAsync(id);

        if (user == null)
        {
          _logger.LogWarning("User not found for ID: {UserId}", id);
          return NotFound(ApiResponse<ResponseResult>.Error(ErrorMessages.UserNotFound));
        }

        return Ok(ApiResponse<SysUserResponseDTO>.Ok(user));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query user with ID: {UserId}", id);
        return StatusCode(500, ApiResponse<string>.Error(ErrorMessages.InternalServerError));
      }
    }






    [HttpGet("group/{groupId}")]
    public async Task<IActionResult> GetByGroupIdAsync(Guid groupId)
    {
      if (groupId == Guid.Empty)
      {
        _logger.LogWarning("Invalid group query request: {Message}", ErrorMessages.GroupIdEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.GroupIdEmpty));
      }

      try
      {
        _logger.LogInformation("Querying users by group ID: {GroupId}", groupId);
        var users = await _sysUserService.QueryUsersByGroupIdAsync(groupId);
        return Ok(ApiResponse<List<SysUserResponseDTO>>.Ok(users));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query users by group ID: {GroupId}", groupId);
        return StatusCode(500, ApiResponse<string>.Error(ErrorMessages.InternalServerError));
      }
    }






    [HttpPost]
    public async Task<IActionResult> CreateAsync([FromBody] SysUserAddDTO dto)
    {
      if (dto == null)
      {
        _logger.LogWarning("Invalid user creation request: {Message}", ErrorMessages.UserDataEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.UserDataEmpty));
      }

      try
      {
        _logger.LogInformation("Creating new user with data: {@Dto}", dto);
        var result = await _sysUserService.AddUserAsync(dto);
        return Ok(ApiResponse<ResponseResult>.Ok(result));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to create user: {@Dto}", dto);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }







    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateAsync(Guid id, [FromBody] SysUserUpdateDTO dto)
    {
      if (id == Guid.Empty || dto == null || id != dto.Id)
      {
        _logger.LogWarning("Invalid user update request: {Message}", ErrorMessages.InvalidUserUpdateRequest);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.InvalidUserUpdateRequest));
      }

      try
      {
        _logger.LogInformation("Updating user with ID: {UserId}", id);
        var result = await _sysUserService.UpdateUserAsync(dto);
        return Ok(ApiResponse<ResponseResult>.Ok(result));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to update user with ID: {UserId}", id);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }






    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteAsync(Guid id)
    {
      if (id == Guid.Empty)
      {
        _logger.LogWarning("Invalid user deletion request: {Message}", ErrorMessages.UserIdEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.UserIdEmpty));
      }

      try
      {
        _logger.LogInformation("Deleting user with ID: {UserId}", id);
        var result = await _sysUserService.DeleteUserByUserIdAsync(id);
        return Ok(ApiResponse<ResponseResult>.Ok(result));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to delete user with ID: {UserId}", id);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }







    [HttpPost("{userId}/groups/{groupId}")]
    public async Task<IActionResult> AssignUserToGroupAsync(Guid userId, Guid groupId)
    {
      if (userId == Guid.Empty || groupId == Guid.Empty)
      {
        _logger.LogWarning("Invalid user-group assignment request: {Message}", ErrorMessages.InvalidUserGroupAssignment);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.InvalidUserGroupAssignment));
      }

      try
      {
        _logger.LogInformation("Assigning user {UserId} to group {GroupId}", userId, groupId);
        var result = await _sysUserService.AssignUserToGroupAsync(userId, groupId);
        return Ok(ApiResponse<ResponseResult>.Ok(result));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to assign user {UserId} to group {GroupId}", userId, groupId);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }







    [HttpDelete("{userId}/groups/{groupId}")]
    public async Task<IActionResult> RemoveUserFromGroupAsync(Guid userId, Guid groupId)
    {
      if (userId == Guid.Empty || groupId == Guid.Empty)
      {
        _logger.LogWarning("Invalid user-group removal request: {Message}", ErrorMessages.InvalidUserGroupAssignment);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.InvalidUserGroupAssignment));
      }

      try
      {
        _logger.LogInformation("Removing user {UserId} from group {GroupId}", userId, groupId);
        var result = await _sysUserService.RemoveUserFromGroupAsync(userId, groupId);
        return Ok(ApiResponse<ResponseResult>.Ok(result));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to remove user {UserId} from group {GroupId}", userId, groupId);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }
  }
}