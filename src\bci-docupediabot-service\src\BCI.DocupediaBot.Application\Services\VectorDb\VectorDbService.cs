﻿using AutoMapper;
using BCI.DocupediaBot.Application.Contracts.Dtos.Content;
using BCI.DocupediaBot.Application.Contracts.Dtos.Embedding;
using BCI.DocupediaBot.Application.Services.CollectPageContent;
using BCI.DocupediaBot.Application.Services.Content;
using BCI.DocupediaBot.Application.Services.LLM;
using BCI.DocupediaBot.Application.Services.SysUser;
using BCI.DocupediaBot.Application.Services.ConfluenceUrl;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.Enums;
using BCI.DocupediaBot.Infrastructure;
using BCI.DocupediaBot.Infrastructure.Abstractions;
using BCI.DocupediaBot.Infrastructure.Configuration;
using BCI.DocupediaBot.Application.Utilities;
using Microsoft.Extensions.Logging;
using Qdrant.Client;
using Qdrant.Client.Grpc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;

using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.VectorDb
{
  public class VectorDbService : IVectorDbService
  {
    private readonly QdrantClient _qdrantClient;
    private readonly IContentService _contentService;
    private readonly IMapper _mapper;
    private readonly ICollectPageContentService _collectPageContentService;
    private readonly ILLMService _llmService;
    private readonly ILogger<VectorDbService> _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ICurrentUserAccessor _currentUserAccessor;
    private readonly ISysUserService _sysUserService;
    private readonly IConfluenceUrlService _confluenceUrlService;

    public VectorDbService(
        QdrantClient qdrantClient,
        IContentService contentService,
        IMapper mapper,
        ICollectPageContentService collectPageContentService,
        ILLMService embeddingService,
        ILogger<VectorDbService> logger,
        IHttpClientFactory httpClientFactory,
        ICurrentUserAccessor currentUserAccessor,
        ISysUserService sysUserService,
        IConfluenceUrlService confluenceUrlService)
    {
      _qdrantClient = qdrantClient ?? throw new ArgumentNullException(nameof(qdrantClient));
      _contentService = contentService ?? throw new ArgumentNullException(nameof(contentService));
      _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
      _collectPageContentService = collectPageContentService ?? throw new ArgumentNullException(nameof(collectPageContentService));
      _llmService = embeddingService ?? throw new ArgumentNullException(nameof(embeddingService));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
      _httpClientFactory = httpClientFactory;
      _currentUserAccessor = currentUserAccessor ?? throw new ArgumentNullException(nameof(currentUserAccessor));
      _sysUserService = sysUserService ?? throw new ArgumentNullException(nameof(sysUserService));
      _confluenceUrlService = confluenceUrlService ?? throw new ArgumentNullException(nameof(confluenceUrlService));
    }

    public async Task ProcessAndStoreTextAsync(ContentResponseDTO dto, Guid collectionId, EmbeddingModel embeddingModel)
    {
      if (dto == null) throw new ArgumentNullException(nameof(dto));

      _logger.LogInformation("Processing and storing text for content ID: {ContentId}", dto.Id);

      await DeletePointsByContentIdAsync(collectionId, dto.Id);

      var chunks = ChunkUtility.ChunkText(dto.ProcessedContent ?? string.Empty,
          ChatbotSettings.Chunk.MaxTokensPerChunk,
          ChatbotSettings.Chunk.OverlapRatio);
      ulong pointId = (ulong)await GetMaxPointIdAsync(collectionId.ToString());

      var chunkList = chunks.ToList();

      var vectors = await _llmService.GetEmbeddingAsync(embeddingModel, chunkList);

      if (vectors != null && vectors.Length == chunkList.Count)
      {
        for (int i = 0; i < chunkList.Count; i++)
        {
          pointId++;
          if (vectors[i] != null)
          {
            var metadata = new { Text = chunkList[i] };
            await StoreInQdrantAsync(vectors[i], metadata, dto, pointId, collectionId.ToString());
          }
        }
      }

      var updateContentDTO = _mapper.Map<ContentUpdateDTO>(dto);
      updateContentDTO.EmbeddingVersionNo = dto.VersionNo;
      await _contentService.UpdateContentAsync(updateContentDTO);

      _logger.LogInformation("Successfully stored text for content ID: {ContentId}", dto.Id);
    }

    public async Task<string> ProcessAndSearchTextAsync(List<string> questionList, Guid collectionId, EmbeddingModel embeddingModel, ChatModel chatModel)
    {
      if (questionList == null || !questionList.Any())
      {
        _logger.LogWarning("ProcessAndSearchTextAsync received null or empty question list.");
        return null;
      }

      _logger.LogInformation("Searching text for collection ID: {CollectionId}", collectionId);

      var allResults = new List<DataItem>();
      var seenPoints = new HashSet<string>();

      var vectors = await _llmService.GetEmbeddingAsync(embeddingModel, questionList);
      if (vectors == null || vectors.Length == 0) return null;
      try
      {
        for (int i = 0; i < vectors.Length; i++)
        {
          if (vectors[i] == null) continue;

          var results = await _qdrantClient.SearchAsync(
              collectionName: collectionId.ToString(),
              vector: vectors[i],
              limit: (ulong)ChatbotSettings.QueryOptions.Limit,
              scoreThreshold: (float)ChatbotSettings.QueryOptions.ScoreThreshold);

          var dataItems = System.Text.Json.JsonSerializer.Deserialize<List<DataItem>>(results.ToString());
          if (dataItems != null && dataItems.Count > 0)
          {
            foreach (var item in dataItems)
            {
              string point = item.Payload.pointId.IntegerValue;
              if (!seenPoints.Contains(point))
              {
                allResults.Add(item);
                seenPoints.Add(point);
              }
            }
          }
        }
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error searching vectors for questions");
        return null;
      }

      if (!allResults.Any()) return null;


      var sortedResults = allResults.OrderByDescending(item => item.Score).ToList();


      string fullContent = BuildFullContent(sortedResults);
      int fullContentTokens = LLMTokenUtility.CountTokens(fullContent);


      int allowedTokens = LLMTokenUtility.GetAllowedInputTokens(chatModel);


      if (fullContentTokens <= allowedTokens)
      {
        _logger.LogInformation("All recalled content fits within token limit ({CurrentTokens}/{AllowedTokens}). Returning complete results.",
            fullContentTokens, allowedTokens);
        return fullContent;
      }


      _logger.LogInformation("Content exceeds token limit ({CurrentTokens}/{AllowedTokens}). Applying intelligent compression.",
          fullContentTokens, allowedTokens);

      return ApplyIntelligentCompression(sortedResults, allowedTokens);
    }

    public async Task DeletePointsByContentIdAsync(Guid collectionId, Guid contentId)
    {
      _logger.LogInformation("Deleting points for content ID: {ContentId} in collection: {CollectionId}", contentId, collectionId);

      var filter = new Filter
      {
        Must = { new Condition { Field = new FieldCondition { Key = "contentId", Match = new Match { Keyword = contentId.ToString() } } } }
      };

      await _qdrantClient.DeleteAsync(collectionId.ToString(), filter);
    }

    public async Task<ResponseResult> ProcessAndUpdateTextAsync(ContentResponseDTO content, Guid collectionId)
    {
      if (content == null) throw new ArgumentNullException(nameof(content));

      _logger.LogInformation("Processing update for content SourceId: {SourceId}", content.SourceId);

      // Get current user token
      var currentUserId = _currentUserAccessor.UserId;
      if (string.IsNullOrEmpty(currentUserId))
      {
        _logger.LogWarning("Unable to get current user ID for content update");
        return new ResponseResult { IsSuccess = false, Msg = "Unable to identify current user" };
      }

      var currentUser = await _sysUserService.QueryUserByNTAccountAsync(currentUserId);
      if (currentUser == null || string.IsNullOrEmpty(currentUser.DocupediaToken))
      {
        _logger.LogWarning("User {UserId} does not have a valid Docupedia token", currentUserId);
        return new ResponseResult { IsSuccess = false, Msg = "Please configure your Docupedia token in your profile" };
      }

      // Extract BaseUrl from content URL
      if (string.IsNullOrEmpty(content.Url))
      {
        _logger.LogWarning("Content URL is missing for SourceId: {SourceId}", content.SourceId);
        return new ResponseResult { IsSuccess = false, Msg = "Content URL is required for update" };
      }

      var baseUrl = _confluenceUrlService.ExtractBaseUrlFromUrl(content.Url);

      string apiUrlForGetContent = $"{baseUrl}/{ChatbotSettings.Docupedia.ApiVersion}/{string.Format(ChatbotSettings.Docupedia.API.GetContentbyId, content.SourceId)}{string.Format(ChatbotSettings.Docupedia.DefaultExpands.FullContent, 0)}";
      string apiUrlForGetContentVersion = $"{baseUrl}/{ChatbotSettings.Docupedia.ApiVersion}/{string.Format(ChatbotSettings.Docupedia.API.GetContentbyId, content.SourceId)}{ChatbotSettings.Docupedia.DefaultExpands.Basic}";

      int currentVersionNo = await _collectPageContentService.GetPageContentVersionAsync(apiUrlForGetContentVersion, content.SourceId, currentUser.DocupediaToken);
      if (content.VersionNo < currentVersionNo)
      {
        var contentDocupediaResponseDTO = await _collectPageContentService.GetPageContentAsync(apiUrlForGetContent, content.SourceId, currentUser.DocupediaToken);
        var updateContentDTO = _mapper.Map<ContentUpdateDTO>(content);

        updateContentDTO.SourceModificationTime = contentDocupediaResponseDTO.Version.When.ToUniversalTime();
        updateContentDTO.VersionNo = currentVersionNo;
        updateContentDTO.OriginContent = contentDocupediaResponseDTO.Body.View.Value;
        string[] processHtml = HtmlUtility.PreprocessHtmlForRag(contentDocupediaResponseDTO.Body.View.Value ?? "");
        updateContentDTO.ProcessedContent = updateContentDTO.Title + ":" + processHtml[0];
        updateContentDTO.SummarizedContent = updateContentDTO.Title + ":" + processHtml[1];

        _logger.LogInformation("Content SourceId: {SourceId} prepared for update", content.SourceId);
        return new ResponseResult
        {
          IsSuccess = true,
          Msg = $"Content {content.SourceId} prepared for update.",
          Data = updateContentDTO
        };
      }

      _logger.LogInformation("No update needed for content SourceId: {SourceId}", content.SourceId);
      return new ResponseResult
      {
        IsSuccess = false,
        Msg = $"Update content {content.SourceId} not needed."
      };
    }

    private async Task StoreInQdrantAsync(float[] vector, object metadata, ContentResponseDTO dto, ulong pointId, string collectionId)
    {
      try
      {
        await _qdrantClient.UpsertAsync(
          collectionName: collectionId,
          points: new List<PointStruct>
          {
            new()
            {
              Id = pointId,
              Vectors = vector,
              Payload = {
                ["metadata"] = metadata.ToString(),
                ["pointId"] = Convert.ToInt32(pointId),
                ["url"] = dto.Url ?? string.Empty,
                ["title"] = dto.Title ?? string.Empty,
                ["contentId"] = dto.Id.ToString(),
                ["sourceId"] = dto.SourceId ?? string.Empty,
                ["versionNo"] = dto.VersionNo.ToString()
              }
            }
          });
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to embedding for content: {ContentId}", dto.Id.ToString());
      }
    }

    private async Task<int> GetMaxPointIdAsync(string collectionName)
    {
      try
      {
        await _qdrantClient.CreatePayloadIndexAsync(collectionName, "pointId", PayloadSchemaType.Integer);
        var orderBy = new OrderBy { Key = "pointId", Direction = Direction.Desc };
        var response = await _qdrantClient.ScrollAsync(collectionName, limit: 1, payloadSelector: true, orderBy: orderBy);

        if (response != null && response.Result.Count > 0)
        {
          return (int)response.Result[0].Id.Num;
        }
        return 0;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to get max point ID for collection: {CollectionName}", collectionName);
        return 0;
      }
    }

    public Task<string> FindDuplicateDocumentsAsync(Guid collectionId)
    {
      throw new NotImplementedException();
    }

    private string BuildFullContent(List<DataItem> results)
    {
      var resultString = new StringBuilder();
      foreach (var item in results)
      {
        resultString.AppendLine($"[Metadata: {item.Payload.Metadata.StringValue}]")
                    .AppendLine($"[Url: {item.Payload.url.StringValue}]")
                    .AppendLine($"[Title: {item.Payload.title.StringValue}]")
                    .AppendLine($"[Score: {item.Score}]")
                    .AppendLine("==========================================================");
      }
      return resultString.ToString();
    }

    private string BuildItemContent(DataItem item)
    {
      return $"[Metadata: {item.Payload.Metadata.StringValue}]\n" +
             $"[Url: {item.Payload.url.StringValue}]\n" +
             $"[Title: {item.Payload.title.StringValue}]\n" +
             $"[Score: {item.Score}]\n" +
             "==========================================================\n";
    }

    private string ApplyIntelligentCompression(List<DataItem> sortedResults, int allowedTokens)
    {
      var selectedResults = new List<DataItem>();
      int currentTokenCount = 0;
      const int MIN_USEFUL_TOKENS = 500;

      for (int i = 0; i < sortedResults.Count; i++)
      {
        var item = sortedResults[i];
        string itemContent = BuildItemContent(item);
        int itemTokens = LLMTokenUtility.CountTokens(itemContent);

        if (currentTokenCount + itemTokens <= allowedTokens)
        {

          selectedResults.Add(item);
          currentTokenCount += itemTokens;
          _logger.LogDebug("Added complete item. Score: {Score}, Tokens: {Tokens}", item.Score, itemTokens);
        }
        else if (selectedResults.Count == 0)
        {

          _logger.LogWarning("First item exceeds token limit. Score: {Score}, Tokens: {Tokens}, Limit: {Limit}. Applying forced summarization.",
              item.Score, itemTokens, allowedTokens);

          string summarized = LLMTokenUtility.SummarizeContent(itemContent, allowedTokens);
          var summarizedItem = CreateSummarizedItem(item, summarized);
          selectedResults.Add(summarizedItem);

          _logger.LogInformation("Forced summarization completed. Original: {OriginalTokens}, Summarized: {SummarizedTokens}",
              itemTokens, LLMTokenUtility.CountTokens(summarized));
          break;
        }
        else
        {

          int remainingTokens = allowedTokens - currentTokenCount;
          if (remainingTokens >= MIN_USEFUL_TOKENS)
          {
            string summarized = LLMTokenUtility.SummarizeContent(itemContent, remainingTokens);
            if (LLMTokenUtility.CountTokens(summarized) >= MIN_USEFUL_TOKENS)
            {
              var summarizedItem = CreateSummarizedItem(item, summarized);
              selectedResults.Add(summarizedItem);
              _logger.LogDebug("Added summarized item. Score: {Score}, Original: {OriginalTokens}, Summarized: {SummarizedTokens}",
                  item.Score, itemTokens, LLMTokenUtility.CountTokens(summarized));
            }
          }
          break;
        }
      }


      if (selectedResults.Count == 0)
      {
        _logger.LogWarning("No content could fit within token limits. Returning fallback message.");
        return "当前选择的知识库内容过大，无法在token限制内处理。请尝试更具体的问题或联系管理员调整配置。";
      }

      return BuildFullContent(selectedResults);
    }

    /// <summary>

    /// </summary>
    private DataItem CreateSummarizedItem(DataItem originalItem, string summarizedContent)
    {

      return new DataItem
      {
        Score = originalItem.Score,
        Payload = new Payload
        {
          Metadata = new BCI.DocupediaBot.Application.Contracts.Dtos.Embedding.Metadata { StringValue = summarizedContent },
          url = originalItem.Payload.url,
          title = originalItem.Payload.title,
          pointId = originalItem.Payload.pointId
        }
      };
    }
  }
}

