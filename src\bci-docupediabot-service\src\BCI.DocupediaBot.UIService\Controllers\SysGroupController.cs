﻿using BCI.DocupediaBot.Application.Contracts.Dtos.SysGroup;
using BCI.DocupediaBot.Application.Services.SysGroup;
using BCI.DocupediaBot.Infrastructure.Constants;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;

namespace BCI.DocupediaBot.UIService.Controllers
{
  [Authorize]
  [ApiController]
  [Route("api/[controller]")]
  public class SysGroupController : ControllerBase
  {
    private readonly ISysGroupService _sysGroupService;
    private readonly ILogger<SysGroupController> _logger;

    public SysGroupController(ISysGroupService sysGroupService, ILogger<SysGroupController> logger)
    {
      _sysGroupService = sysGroupService ?? throw new ArgumentNullException(nameof(sysGroupService));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }





    [HttpGet]
    public async Task<IActionResult> GetAllAsync()
    {
      try
      {
        _logger.LogInformation("Querying all groups");
        var groups = await _sysGroupService.QueryGroupsAsync();
        return Ok(ApiResponse<List<SysGroupResponseDTO>>.Ok(groups));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query groups");
        return StatusCode(500, ApiResponse<string>.Error(ErrorMessages.InternalServerError));
      }
    }






    [HttpGet("{id}")]
    public async Task<IActionResult> GetByIdAsync(Guid id)
    {
      if (id == Guid.Empty)
      {
        _logger.LogWarning("Invalid group query request: {Message}", ErrorMessages.GroupIdEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.GroupIdEmpty));
      }

      try
      {
        _logger.LogInformation("Querying group with users for ID: {GroupId}", id);
        var group = await _sysGroupService.QueryGroupWithUsersAsync(id);

        if (group == null)
        {
          _logger.LogWarning("Group not found for ID: {GroupId}", id);
          return NotFound(ApiResponse<ResponseResult>.Error(ErrorMessages.GroupNotFound));
        }

        return Ok(ApiResponse<SysGroupResponseDTO>.Ok(group));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query group with ID: {GroupId}", id);
        return StatusCode(500, ApiResponse<string>.Error(ErrorMessages.InternalServerError));
      }
    }






    [HttpPost]
    public async Task<IActionResult> CreateAsync([FromBody] SysGroupAddDTO dto)
    {
      if (dto == null)
      {
        _logger.LogWarning("Invalid group creation request: {Message}", ErrorMessages.GroupDataEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.GroupDataEmpty));
      }

      try
      {
        _logger.LogInformation("Creating new group with data: {@Dto}", dto);
        var result = await _sysGroupService.AddGroupAsync(dto);
        return Ok(ApiResponse<ResponseResult>.Ok(result));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to create group: {@Dto}", dto);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }







    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateAsync(Guid id, [FromBody] SysGroupUpdateDTO dto)
    {
      if (id == Guid.Empty || dto == null || id != dto.Id)
      {
        _logger.LogWarning("Invalid group update request: {Message}", ErrorMessages.InvalidGroupUpdateRequest);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.InvalidGroupUpdateRequest));
      }

      try
      {
        _logger.LogInformation("Updating group with ID: {GroupId}", id);
        var result = await _sysGroupService.UpdateGroupAsync(dto);
        return Ok(ApiResponse<ResponseResult>.Ok(result));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to update group with ID: {GroupId}", id);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }






    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteAsync(Guid id)
    {
      if (id == Guid.Empty)
      {
        _logger.LogWarning("Invalid group deletion request: {Message}", ErrorMessages.GroupIdEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.GroupIdEmpty));
      }

      try
      {
        _logger.LogInformation("Deleting group with ID: {GroupId}", id);
        var result = await _sysGroupService.DeleteGroupByGroupIdAsync(id);
        return Ok(ApiResponse<ResponseResult>.Ok(result));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to delete group with ID: {GroupId}", id);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }
  }
}