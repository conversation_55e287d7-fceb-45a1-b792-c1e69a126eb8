﻿using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Infrastructure.Abstractions;
using BCI.DocupediaBot.Infrastructure.Entity;
using Bosch.Foundation.Abstractions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using System.Reflection;

namespace BCI.DocupediaBot.Infrastructure.Database.Context
{
  public class DocupediaBotDbContext : DbContext
  {
    private readonly ICurrentUserAccessor _currentUserAccessor;
    private readonly ITenantAccessor _tenantAccessor;

    public DocupediaBotDbContext(
        DbContextOptions<DocupediaBotDbContext> options,
        ICurrentUserAccessor currentUserAccessor = null,
        ITenantAccessor tenantAccessor = null) : base(options)
    {
      _currentUserAccessor = currentUserAccessor;
      _tenantAccessor = tenantAccessor;
    }


    public DbSet<Content> Contents => Set<Content>();
    public DbSet<Page> Pages => Set<Page>();
    public DbSet<Collection> Collections => Set<Collection>();
    public DbSet<PagesInCollection> PagesInCollections => Set<PagesInCollection>();
    public DbSet<ContentsInPage> ContentsInPages => Set<ContentsInPage>();
    public DbSet<ChatHistory> ChatHistories => Set<ChatHistory>();
    public DbSet<SysUser> SysUsers => Set<SysUser>();
    public DbSet<SysGroup> SysGroups => Set<SysGroup>();
    public DbSet<SysUsersInGroup> SysUsersInGroups => Set<SysUsersInGroup>();

    public override int SaveChanges()
    {
      ApplyAuditInformation();
      return base.SaveChanges();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
      ApplyAuditInformation();
      return await base.SaveChangesAsync(cancellationToken);
    }

    private void ApplyAuditInformation()
    {
      var currentTime = DateTime.UtcNow;
      var currentUserId = _currentUserAccessor?.UserId ?? "System";
      var currentTenantId = _tenantAccessor?.TenantId ?? string.Empty;

      var entries = ChangeTracker.Entries()
          .Where(e => e.Entity is IEntity &&
                     (e.State == EntityState.Added ||
                      e.State == EntityState.Modified ||
                      e.State == EntityState.Deleted));

      foreach (var entry in entries)
      {
        var entity = (IEntity)entry.Entity;

        switch (entry.State)
        {
          case EntityState.Added:

            if (entity is IAuditEntity auditableEntity)
            {
              auditableEntity.CreationTime = currentTime;
              auditableEntity.Creator = currentUserId;
              auditableEntity.ModificationTime = currentTime;
              auditableEntity.Modifier = currentUserId;
            }


            if (entity is ITenantEntity tenantEntity && string.IsNullOrEmpty(tenantEntity.TenantId))
            {
              tenantEntity.TenantId = currentTenantId;
            }


            if (entity is ISoftDeleteEntity softDeleteEntity)
            {
              softDeleteEntity.IsDeleted = false;
            }
            break;

          case EntityState.Modified:

            if (entity is IAuditEntity modifiedAuditableEntity)
            {
              modifiedAuditableEntity.ModificationTime = currentTime;
              modifiedAuditableEntity.Modifier = currentUserId;


              entry.Property(nameof(IAuditEntity.CreationTime)).IsModified = false;
              entry.Property(nameof(IAuditEntity.Creator)).IsModified = false;
            }


            if (entity is ITenantEntity)
            {
              entry.Property(nameof(ITenantEntity.TenantId)).IsModified = false;
            }
            break;
        }
      }
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
      base.OnConfiguring(optionsBuilder);
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
      modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
      base.OnModelCreating(modelBuilder);
    }
  }
}