:host {
  bci-page-content {
    display: flex;
    flex-direction: column;
  }
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 80vh;
  width: 90%;
  margin: 0 auto;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  font-family: 'Arial', sans-serif;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  background-color: #f9f9f9;
}

.chat-header {
  text-align: center;
  padding: 20px;
  color: #555;
}

.header-logo img {
  max-width: 200px;
  margin-bottom: 10px;
}

.page-heading {
  font-size: 1.5em;
  font-weight: 500;
  margin: 10px 0;
}

.chat-header p {
  margin: 5px 0;
  color: #666;
}

.usage-instructions .highlight {
  background-color: #fff3cd;
  color: #856404;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.usage-instructions {
  margin: 10px 0;
  text-align: left;
  color: #666;
}

.usage-instructions h3 {
  font-size: 1.2em;
  font-weight: 500;
  margin-bottom: 10px;
  color: #005670;
}

.usage-instructions ol {
  padding-left: 0;
  margin: 0;
  list-style: none;
}

.usage-instructions li {
  margin: 10px 0;
  line-height: 1.5;
  display: flex;
  align-items: flex-start;
  color: #333;
}

.usage-instructions .step-icon {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  margin-right: 10px;
  font-size: 1em;
  font-weight: bold;
  color: #fff;
  background-color: #00a1d6;
  border-radius: 50%;
  text-align: center;
}

.usage-selection {
  margin-top: 50px;
}

.chat-messages {
  flex-grow: 1;
  padding: 15px;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 8px 8px 0 0;
  display: flex;
  flex-direction: column;
}

.chat-input {
  display: flex;
  padding: 15px;
  margin-bottom: 15px;
  border-top: 1px solid #e0e0e0;
  transition: transform 0.3s ease, opacity 0.3s ease;
  background-color: #f9f9f9;
}

.chat-input.centered {
  justify-content: center;
  align-items: center;
  height: 100%;
  border: none;
  opacity: 0.9;
}

.chat-input.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.chat-input input {
  flex-grow: 1;
  padding: 10px;
  font-size: 1em;
  border-radius: 6px;
  border: 1px solid #d0d0d0;
  background-color: #fff;
  transition: border-color 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
}

.chat-input input:hover {
  border-color: #007bff;
  background-color: #f8f9ff;
}

.chat-input input:focus {
  border-color: #007bff;
  background-color: #f8f9ff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.chat-input button {
  padding: 10px 20px;
  margin-left: 10px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.chat-input button:hover {
  background-color: #5a6268;
}

.message-container {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.message-container.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin: 0 8px;
  flex-shrink: 0;
}

.user-message {
  padding: 12px 18px;
  background-color: #6c757d;
  color: white;
  border-radius: 20px;
  max-width: 80%;
  word-wrap: break-word;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: inline-block;
}

.bot-message {
  background-color: #f1f1f1;
  color: #333;
  padding: 12px 18px;
  border-radius: 20px;
  max-width: 85%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
}

.loading-message {
  background-color: #f1f1f1;
  color: #333;
  padding: 12px 18px;
  border-radius: 20px;
  max-width: 85%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
}

.loading-dots {
  display: inline-flex;
  align-items: center;
}

.loading-dots::after {
  content: '';
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #666;
  margin-left: 4px;
  animation: loading-dot 1.4s infinite ease-in-out;
}

.loading-dots::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #666;
  margin-right: 4px;
  animation: loading-dot 1.4s infinite ease-in-out 0.32s;
}

.loading-dots {
  position: relative;
}

.loading-dots span {
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #666;
  animation: loading-dot 1.4s infinite ease-in-out 0.16s;
}

@keyframes loading-dot {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}

.bot-message a {
  color: #6c757d;
  text-decoration: none;
  padding: 2px 6px;
  border-radius: 4px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.bot-message a:hover {
  background-color: #e0e0e0;
  color: #333;
  text-decoration: underline;
}

.bot-message table {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  display: block;
  overflow-x: auto;
}

.bot-message th,
.bot-message td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
  white-space: nowrap;
}

.bot-message th {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #555;
}

.bot-message tr:hover {
  background-color: #f9f9f9;
  transition: background-color 0.2s ease;
}

.bot-message td a {
  color: #6c757d;
  text-decoration: none;
}

.bot-message td a:hover {
  background-color: #e0e0e0;
  color: #333;
}

.bot-message img {
  max-width: 80%;
  margin: 15px 0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  display: block;
}

.bot-message img:hover {
  transform: scale(1.02);
}

.bot-message pre {
  background-color: #2d2d2d;
  padding: 15px;
  border-radius: 6px;
  margin: 15px 0;
  overflow-x: auto;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: #ccc !important;
}

.bot-message pre code {
  font-family: 'Courier New', Courier, monospace;
  white-space: pre-wrap;
  color: inherit;
}

.bot-message ul, .bot-message ol {
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.bot-message ul li, .bot-message ol li {
  padding: 4px 8px;
  border-bottom: 1px solid #e0e0e0;
  color: #333;
  line-height: 1.4;
}

.bot-message ul li:last-child, .bot-message ol li:last-child {
  border-bottom: none;
}

.bot-message ul li:hover,
.bot-message ol li:hover {
  background-color: #f9f9f9;
  transition: background-color 0.2s ease;
}

.bot-message .panel {
  margin: 15px 0;
  padding: 0;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  width: 60%;
}

.bot-message .info {
  border-left: 4px solid #007bff;
  padding: 10px 20px;
}

.bot-message .warning {
  border-left: 4px solid #ffc107;
  padding: 10px 20px;
}

.bot-message .panel:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.chat-input button:nth-child(3) {
  margin-left: 10px;
  background-color: #dc3545;
}

.chat-input button:nth-child(3):hover {
  background-color: #c82333;
}

mat-form-field {
  margin-right: 20px;
}
