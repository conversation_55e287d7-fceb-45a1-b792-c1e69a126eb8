﻿using AutoMapper;
using BCI.DocupediaBot.Application.Contracts.Dtos.Content;
using BCI.DocupediaBot.Application.Contracts.Dtos.Page;
using BCI.DocupediaBot.Application.Services.Content;
using BCI.DocupediaBot.Infrastructure;
using BCI.DocupediaBot.Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.CollectPageContent
{
  public class CollectPageContentService : ICollectPageContentService
	{
		private readonly IHttpClientFactory _httpClientFactory;
		private readonly IContentService _contentService;
		private readonly IMapper _mapper;
		private readonly ILogger<CollectPageContentService> _logger;

		public CollectPageContentService(
				IHttpClientFactory httpClientFactory,
				IContentService contentService,
				IMapper mapper,
				ILogger<CollectPageContentService> logger)
		{
			_httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
			_contentService = contentService ?? throw new ArgumentNullException(nameof(contentService));
			_mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
			_logger = logger ?? throw new ArgumentNullException(nameof(logger));
		}

		public async Task CollectPageContentRecursiveAsync(PageResponseDTO pageDto, string userToken, string baseUrl)
		{
			if (pageDto == null || string.IsNullOrEmpty(pageDto.SourceId))
			{
				_logger.LogWarning("CollectPageContentRecursiveAsync received null or invalid page DTO.");
				throw new ArgumentException("Page data cannot be null or have an empty SourceId.");
			}

			if (string.IsNullOrWhiteSpace(userToken))
			{
				throw new ArgumentException("User token is required.");
			}

			if (string.IsNullOrWhiteSpace(baseUrl))
			{
				throw new ArgumentException("Base URL is required.");
			}

			_logger.LogInformation("Collecting page content recursively for page ID: {PageId}, SourceId: {SourceId}", pageDto.Id, pageDto.SourceId);

			var allPageContents = new List<AllPageContentsDTO>();
			await GetAllContentsWithBFSAsync(allPageContents, pageDto, userToken, baseUrl);

			if (!allPageContents.Any())
			{
				_logger.LogInformation("No contents collected for page ID: {PageId}", pageDto.Id);
				return;
			}

			IEnumerable<ContentAddDTO> contentDtos;
			try
			{
				contentDtos = allPageContents.Select(item =>
				{
					var dto = _mapper.Map<ContentAddDTO>(item.ContentResponse);
					dto.Url = item.WebUrl ?? "";
          string[] processHtml = HtmlUtility.PreprocessHtmlForRag(item.ContentResponse.Body?.View?.Value ?? "");
          dto.ProcessedContent = dto.Title + ":" + processHtml[0];
          dto.SummarizedContent = dto.Title + ":" + processHtml[1];

					return dto;
				}).ToList();
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "Failed to map contents for page ID: {PageId}", pageDto.Id);
				throw new InvalidOperationException($"Failed to map content: {ex.Message}", ex);
			}

			var addResult = await _contentService.AddContentsAsync(contentDtos, pageDto.Id);
			if (!addResult.IsSuccess)
			{
				_logger.LogError("Failed to add contents for page ID: {PageId}. Error: {Error}", pageDto.Id, addResult.Msg);
				throw new InvalidOperationException($"Failed to add contents: {addResult.Msg}");
			}

			_logger.LogInformation("Successfully collected and stored {Count} contents for page ID: {PageId}", allPageContents.Count, pageDto.Id);
		}

		private async Task GetAllContentsWithBFSAsync(List<AllPageContentsDTO> allPageContents, PageResponseDTO pageDto, string userToken, string baseUrl)
		{
			var resultQueue = new Queue<AllPageContentsDTO>();
			var rootApiUrl = $"{baseUrl}/{ChatbotSettings.Docupedia.ApiVersion}/{string.Format(ChatbotSettings.Docupedia.API.GetContentbyId, pageDto.SourceId)}{string.Format(ChatbotSettings.Docupedia.DefaultExpands.FullContent, 0)}";
			var rootWebUrl = $"{baseUrl}/pages/viewpage.action?pageId={pageDto.SourceId}";

			var rootPageContent = await GetPageContentAsync(rootApiUrl, pageDto.SourceId, userToken);
			if (rootPageContent == null)
			{
				_logger.LogWarning("Root page content not found for SourceId: {SourceId}", pageDto.SourceId);
				return;
			}

			rootPageContent.Path = $"/{rootPageContent.Title}";
			resultQueue.Enqueue(new AllPageContentsDTO { ContentResponse = rootPageContent, WebUrl = rootWebUrl });

			while (resultQueue.Count > 0)
			{
				var current = resultQueue.Dequeue();
				allPageContents.Add(current);

				if (!pageDto.IsIncludeChild || current.ContentResponse.Children?.Page?.Results == null || !current.ContentResponse.Children.Page.Results.Any())
				{
					continue;
				}

				int depth = current.ContentResponse.Path.Split('/').Length - 1;
				var children = current.ContentResponse.Children.Page.Results.Take(ChatbotSettings.Docupedia.PageLimit - resultQueue.Count);

				foreach (var child in children)
				{
					var childApiUrl = $"{baseUrl}/{ChatbotSettings.Docupedia.ApiVersion}/{string.Format(ChatbotSettings.Docupedia.API.GetContentbyId, child.Id)}{string.Format(ChatbotSettings.Docupedia.DefaultExpands.FullContent, depth)}";
					var childWebUrl = $"{baseUrl}/pages/viewpage.action?pageId={child.Id}";
					var childPageContent = await GetPageContentAsync(childApiUrl, child.Id, userToken);

					if (childPageContent == null)
					{
						_logger.LogWarning("Child page content not found for ID: {ChildId}", child.Id);
						continue;
					}

					childPageContent.Path = $"{current.ContentResponse.Path}/{child.Title}";
					resultQueue.Enqueue(new AllPageContentsDTO { ContentResponse = childPageContent, WebUrl = childWebUrl });
				}
			}
		}

		public async Task<ContentDocupediaResponseDTO> GetPageContentAsync(string apiUrl, string sourceId, string userToken)
		{
			if (string.IsNullOrEmpty(apiUrl) || string.IsNullOrEmpty(sourceId))
			{
				_logger.LogWarning("GetPageContentAsync received null or empty apiUrl or sourceId.");
				return null;
			}

			if (string.IsNullOrWhiteSpace(userToken))
			{
				throw new ArgumentException("User token is required.");
			}

			_logger.LogInformation("Fetching page content for SourceId: {SourceId}", sourceId);

			using var httpClient = _httpClientFactory.CreateClient("ConfluenceClient");
			httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", userToken);

			try
			{
				var response = await httpClient.GetAsync(apiUrl);
				response.EnsureSuccessStatusCode();

				var responseJson = await response.Content.ReadAsStringAsync();
				if (string.IsNullOrWhiteSpace(responseJson))
				{
					_logger.LogWarning("Empty response received for SourceId: {SourceId}", sourceId);
					return null;
				}

				var content = JsonSerializer.Deserialize<ContentDocupediaResponseDTO>(responseJson);
				return content;
			}
			catch (HttpRequestException ex)
			{
				_logger.LogError(ex, "Failed to fetch content from {ApiUrl} for SourceId: {SourceId}", apiUrl, sourceId);
				throw new ApplicationException($"Failed to get content from {apiUrl}, StatusCode: {ex.StatusCode}", ex);
			}
			catch (JsonException ex)
			{
				_logger.LogError(ex, "Failed to deserialize content for SourceId: {SourceId}", sourceId);
				return null;
			}
		}

		public async Task<int> GetPageContentVersionAsync(string apiUrl, string sourceId, string userToken)
		{
			if (string.IsNullOrEmpty(apiUrl) || string.IsNullOrEmpty(sourceId))
			{
				_logger.LogWarning("GetPageContentVersionAsync received null or empty apiUrl or sourceId.");
				return 0;
			}

			if (string.IsNullOrWhiteSpace(userToken))
			{
				throw new ArgumentException("User token is required.");
			}

			_logger.LogInformation("Fetching page content version for SourceId: {SourceId}", sourceId);

			using var httpClient = _httpClientFactory.CreateClient("ConfluenceClient");
			httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", userToken);

			try
			{
				var response = await httpClient.GetAsync(apiUrl);
				response.EnsureSuccessStatusCode();

				var responseJson = await response.Content.ReadAsStringAsync();
				if (string.IsNullOrWhiteSpace(responseJson))
				{
					_logger.LogWarning("Empty response received for SourceId: {SourceId}", sourceId);
					return 0;
				}

				var content = JsonSerializer.Deserialize<ContentDocupediaResponseDTO>(responseJson);
				return content?.Version?.Number ?? 0;
			}
			catch (HttpRequestException ex)
			{
				_logger.LogError(ex, "Failed to fetch version from {ApiUrl} for SourceId: {SourceId}", apiUrl, sourceId);
				throw new ApplicationException($"Failed to get version from {apiUrl}, StatusCode: {ex.StatusCode}", ex);
			}
			catch (JsonException ex)
			{
				_logger.LogError(ex, "Failed to deserialize version for SourceId: {SourceId}", sourceId);
				return 0;
			}
		}
	}
}