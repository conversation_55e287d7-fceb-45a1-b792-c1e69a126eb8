<bci-page-content>
  <div class="chat-container">
    <header class="chat-header" *ngIf="!hasInteracted">
      <div class="usage-instructions">
        <h3>How to Unleash the Power of Docupedia Chatbot?</h3>
        <ol>
          <li><span class="step-icon">1</span> Kick things off by adding your own virtual Collection on the <span class="highlight">Collection Management</span> tab – it's your gateway to brilliance!</li>
          <li><span class="step-icon">2</span> <span class="highlight">Supercharge</span> your Collection with Pages you care about from <span class="highlight">any Space</span> – we'll magically handle all subpages for you.</li>
          <li><span class="step-icon">3</span> Hit the <span class="highlight">Embedding button</span> to transform your pages into a treasure trove of vectorized content – ready in a snap!</li>
          <li><span class="step-icon">4</span> Once everything's set, watch the Collection <span class="highlight">light up in green</span> – your signal to dive in and start chatting!</li>
          <li><span class="step-icon">5</span> <span class="highlight">Select the model</span> and experience the power of your Docupedia Chatbot!</li>
        </ol>
      </div>
      <div class="usage-selection">
        <mat-form-field>
          <mat-label>LLM</mat-label>
          <mat-select
            placeholder="Select"
            [(ngModel)]="selectedModel"
            (selectionChange)="onModelChange()"
            [disabled]="chatModels.length === 0"
          >
            <mat-option *ngFor="let model of chatModels" [value]="model.value">
              {{model.name}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field [ngStyle]="{ 'width': '250px' }">
          <mat-label>Collection</mat-label>
          <mat-select
            placeholder="Select"
            [(ngModel)]="selectedCollection"
            (selectionChange)="onCollectionChange()"
            [disabled]="filteredCollections.length === 0"
          >
            <mat-option *ngFor="let collection of filteredCollections" [value]="collection.id">
              {{collection.name}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field [ngStyle]="{ 'width': '350px' }">
          <mat-label>Explore our exciting new features!</mat-label>
          <mat-select
            placeholder="Have a try"
            [(ngModel)]="selectedCollectionAction"
            (selectionChange)="onCollectionActionChange()"
          >
            <mat-option value="summarize">Summing the knowledge of collection</mat-option>
            <mat-option value="recent">Lists the recently updated pages</mat-option>
            <mat-option value="duplicates">Find pages with duplicate contents</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </header>

    <div class="chat-messages" #chatMessages *ngIf="messages.length > 0">
      <div
        *ngFor="let message of messages"
        class="message-container"
        [ngClass]="message.type"
      >
        <img
          class="message-avatar"
          [src]="message.type === 'user' ? './assets/img/user.png' : './assets/img/bot.png'"
          [alt]="message.type === 'user' ? 'User' : 'Bot'"
          [title]="message.type === 'user' ? 'User Avatar' : 'Bot Avatar'"
        >
        <div
          [ngClass]="message.type + '-message'"
          [innerHTML]="message.type === 'bot' ? message.formattedContent : message.content"
        >
        </div>
      </div>

      <!-- Loading message -->
      <div *ngIf="isLoading" class="message-container bot">
        <img
          class="message-avatar"
          src="./assets/img/bot.png"
          alt="Bot"
        >
        <div class="loading-message">
          <div class="loading-dots">
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <div class="chat-input" [ngClass]="{'centered': !hasInteracted, 'disabled': isLoading}">
      <input
        type="text"
        id="userInput"
        #userInput
        placeholder="Type your message..."
        (keypress)="onKeyPress($event)"
        [disabled]="isLoading"
      />
      <button type="button" (click)="sendMessage()" [disabled]="isLoading">Send</button>
      <button type="button" (click)="resetChat()" *ngIf="hasInteracted" [disabled]="isLoading">Reset</button>
    </div>
  </div>
</bci-page-content>
